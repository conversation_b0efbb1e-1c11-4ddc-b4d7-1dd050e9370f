'use client';

import React, { useState, useRef, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { ZoomIn, ZoomOut, X, Maximize2, Minimize2 } from 'lucide-react';
import * as Tooltip from '@radix-ui/react-tooltip';
import MLComponentDetail from './MLComponentDetail';

interface ComponentDetails {
  description: string;
  features: string[];
  benefits: string[];
  icon: string;
  implementationTech?: string[];
  technicalConsiderations?: string[];
}

interface InteractiveMLArchitectureTranslations {
  title: string;
  subtitle: string;
  phases: {
    dataIngestion: string;
    modelTraining: string;
    deployment: string;
    monitoring: string;
  };
  viewModes: {
    conceptual: string;
    implementation: string;
  };
  components: {
    // Detail modal keys
    keyFeatures: string;
    benefits: string;
    implementationTech: string;
    technicalConsiderations: string;
    componentWorkflow: string;
    implementationArchitecture: string;
    comingSoon: string;
    learnMore: string;
  };
  buttons: {
    zoomIn: string;
    zoomOut: string;
    fullscreen: string;
    close: string;
    runData: string;
    reset: string;
  };
  // Shared componentDetails from ML tabs
  componentDetails: Record<string, ComponentDetails>;
  interactive?: {
    title?: string;
    subtitle?: string;
    buttons?: {
      zoomIn?: string;
      zoomOut?: string;
      fullscreen?: string;
      close?: string;
      runData?: string;
      reset?: string;
    };
    components?: Record<string, {
      name?: string;
      description?: string;
      icon?: string;
      features?: string[];
      benefits?: string[];
      technologies?: string[];
    }>;
    connections?: {
      rawData?: string;
      cleanedData?: string;
      selectedFeatures?: string;
      validatedData?: string;
      modelArchitecture?: string;
      trainedModels?: string;
      evaluationMetrics?: string;
      selectedModel?: string;
      deployedModel?: string;
      predictionService?: string;
      infrastructure?: string;
      forecasts?: string;
      performanceMetrics?: string;
      alerts?: string;
      notifications?: string;
      retrainingSignal?: string;
      dataQualityAlerts?: string;
      predictions?: string;
      dataRefreshSignal?: string;
    };
  };
}

interface InteractiveMLArchitectureProps {
  isOpen: boolean;
  onClose: () => void;
  translations: InteractiveMLArchitectureTranslations;
}

// Define component and connection types
interface ArchComponent {
  id: string;
  name: string;
  description: string;
  phase: 'dataIngestion' | 'modelTraining' | 'deployment' | 'monitoring';
  x: number; // Percentage position
  y: number; // Percentage position
  icon?: string;
  color?: string;
  features?: string[];
  benefits?: string[];
  technologies?: string[]; // For implementation view
}

interface Connection {
  source: string;
  target: string;
  dataType?: string; // What kind of data flows through this connection
  animated?: boolean;
}



const InteractiveMLArchitecture: React.FC<InteractiveMLArchitectureProps> = ({
  isOpen,
  onClose,
  translations
}) => {
  // State management
  const [selectedComponent, setSelectedComponent] = useState<string | null>(null);
  const [zoomLevel, setZoomLevel] = useState(1);
  const [viewMode, setViewMode] = useState<'conceptual' | 'implementation'>('conceptual');
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [isAnimating, setIsAnimating] = useState(false);
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });

  // Component mapping with mathematically corrected node distribution for optimal visual clarity
  // Positions calculated to maximize visual separation and ensure proper container alignment
  const componentMapping = [
    // Data Ingestion Phase - Y position: 12 (centered in container Y: 2-22)
    { id: 'dataCollector', phase: 'dataIngestion' as const, x: 15, y: 12, color: '#3B82F6' }, // Left position with adequate margin
    { id: 'dataPreprocessor', phase: 'dataIngestion' as const, x: 35, y: 12, color: '#3B82F6' }, // 20% spacing for clarity
    { id: 'featureSelector', phase: 'dataIngestion' as const, x: 55, y: 12, color: '#3B82F6' }, // Balanced center positioning
    { id: 'dataQualityMonitor', phase: 'dataIngestion' as const, x: 75, y: 12, color: '#3B82F6' }, // Right position with margin

    // Model Training Phase - Y position: 36 (centered in container Y: 26-46)
    { id: 'modelBuilder', phase: 'modelTraining' as const, x: 15, y: 36, color: '#8B5CF6' }, // Aligned with data ingestion
    { id: 'modelTrainer', phase: 'modelTraining' as const, x: 35, y: 36, color: '#8B5CF6' }, // Consistent 20% spacing
    { id: 'modelEvaluator', phase: 'modelTraining' as const, x: 55, y: 36, color: '#8B5CF6' }, // Center alignment maintained
    { id: 'automaticModelSelector', phase: 'modelTraining' as const, x: 75, y: 36, color: '#8B5CF6' }, // Right side consistency

    // Deployment Phase - Y position: 60 (centered in container Y: 50-70)
    { id: 'modelDeployer', phase: 'deployment' as const, x: 15, y: 60, color: '#10B981' }, // Vertical alignment maintained
    { id: 'modelPredictor', phase: 'deployment' as const, x: 35, y: 60, color: '#10B981' }, // Optimal horizontal distribution
    { id: 'kubernetesCluster', phase: 'deployment' as const, x: 55, y: 60, color: '#10B981' }, // Center positioning preserved
    { id: 'forecastService', phase: 'deployment' as const, x: 75, y: 60, color: '#10B981' }, // Right margin consistency

    // Monitoring Phase - Y position: 84 (centered in container Y: 74-94)
    { id: 'alertProcessor', phase: 'monitoring' as const, x: 15, y: 84, color: '#EC4899' }, // Perfect vertical alignment
    { id: 'predictionsMonitor', phase: 'monitoring' as const, x: 35, y: 84, color: '#EC4899' }, // Optimal spacing maintained
    { id: 'notificationService', phase: 'monitoring' as const, x: 55, y: 84, color: '#EC4899' }, // Center column alignment
    { id: 'retrainingTrigger', phase: 'monitoring' as const, x: 75, y: 84, color: '#EC4899' }, // Right column consistency
  ];

  // Create component data using shared componentDetails from ML tabs
  const mlComponents: ArchComponent[] = componentMapping.map(config => {
    // Access the correct translation path: translations.interactive.components
    const componentDetails = translations.interactive?.components?.[config.id];

    // Create fallback component data if translations are missing
    const fallbackComponent = {
      id: config.id,
      name: config.id.charAt(0).toUpperCase() + config.id.slice(1).replace(/([A-Z])/g, ' $1').trim(),
      description: `${config.id} component description`,
      phase: config.phase,
      x: config.x,
      y: config.y,
      icon: '⚙️', // Default icon
      color: config.color,
      features: ['Feature 1', 'Feature 2'],
      benefits: ['Benefit 1', 'Benefit 2'],
      technologies: ['Technology 1', 'Technology 2']
    };

    if (!componentDetails) {
      console.warn(`Component details not found for: ${config.id}, using fallback`);
      return fallbackComponent;
    }

    return {
      id: config.id,
      name: componentDetails.name || config.id.charAt(0).toUpperCase() + config.id.slice(1).replace(/([A-Z])/g, ' $1').trim(),
      description: componentDetails.description || fallbackComponent.description,
      phase: config.phase,
      x: config.x,
      y: config.y,
      icon: componentDetails.icon || fallbackComponent.icon,
      color: config.color,
      features: componentDetails.features || fallbackComponent.features,
      benefits: componentDetails.benefits || fallbackComponent.benefits,
      technologies: componentDetails.technologies || fallbackComponent.technologies
    };
  });



  // Fallback translations
  const fallbackTranslations = {
    title: 'Interactive ML Architecture Explorer',
    subtitle: 'Explore our comprehensive machine learning architecture',
    phases: {
      dataIngestion: 'Data Ingestion & Preprocessing',
      modelTraining: 'Model Training & Evaluation',
      deployment: 'Model Deployment & Serving',
      monitoring: 'Monitoring & Feedback'
    },
    viewModes: {
      conceptual: 'Conceptual View',
      implementation: 'Implementation View'
    },
    buttons: {
      zoomIn: 'Zoom In',
      zoomOut: 'Zoom Out',
      fullscreen: 'Toggle Fullscreen',
      close: 'Close',
      runData: 'Run Test Data',
      reset: 'Reset View'
    }
  };

  // Merge translations with fallbacks
  const safeTranslations = {
    title: translations.title || fallbackTranslations.title,
    subtitle: translations.subtitle || fallbackTranslations.subtitle,
    phases: {
      dataIngestion: translations.phases?.dataIngestion || fallbackTranslations.phases.dataIngestion,
      modelTraining: translations.phases?.modelTraining || fallbackTranslations.phases.modelTraining,
      deployment: translations.phases?.deployment || fallbackTranslations.phases.deployment,
      monitoring: translations.phases?.monitoring || fallbackTranslations.phases.monitoring
    },
    viewModes: {
      conceptual: translations.viewModes?.conceptual || fallbackTranslations.viewModes.conceptual,
      implementation: translations.viewModes?.implementation || fallbackTranslations.viewModes.implementation
    },
    buttons: {
      zoomIn: translations.buttons?.zoomIn || fallbackTranslations.buttons.zoomIn,
      zoomOut: translations.buttons?.zoomOut || fallbackTranslations.buttons.zoomOut,
      fullscreen: translations.buttons?.fullscreen || fallbackTranslations.buttons.fullscreen,
      close: translations.buttons?.close || fallbackTranslations.buttons.close,
      runData: translations.buttons?.runData || fallbackTranslations.buttons.runData,
      reset: translations.buttons?.reset || fallbackTranslations.buttons.reset
    }
  };

  // Create connection data using simple fallback strings since interactive.connections might not exist
  const mlConnections: Connection[] = [
    // Data Ingestion Phase connections
    { source: 'dataCollector', target: 'dataPreprocessor', dataType: 'Raw Data' },
    { source: 'dataPreprocessor', target: 'featureSelector', dataType: 'Cleaned Data' },
    { source: 'featureSelector', target: 'dataQualityMonitor', dataType: 'Selected Features' },
    { source: 'dataQualityMonitor', target: 'modelBuilder', dataType: 'Validated Data' },

    // Model Training Phase connections
    { source: 'modelBuilder', target: 'modelTrainer', dataType: 'Model Architecture' },
    { source: 'modelTrainer', target: 'modelEvaluator', dataType: 'Trained Models' },
    { source: 'modelEvaluator', target: 'automaticModelSelector', dataType: 'Evaluation Metrics' },
    { source: 'automaticModelSelector', target: 'modelDeployer', dataType: 'Selected Model' },

    // Deployment Phase connections
    { source: 'modelDeployer', target: 'modelPredictor', dataType: 'Deployed Model' },
    { source: 'modelPredictor', target: 'kubernetesCluster', dataType: 'Prediction Service' },
    { source: 'kubernetesCluster', target: 'forecastService', dataType: 'Infrastructure' },
    { source: 'forecastService', target: 'predictionsMonitor', dataType: 'Forecasts' },

    // Monitoring Phase connections
    { source: 'predictionsMonitor', target: 'alertProcessor', dataType: 'Performance Metrics' },
    { source: 'alertProcessor', target: 'notificationService', dataType: 'Alerts' },
    { source: 'notificationService', target: 'retrainingTrigger', dataType: 'Notifications' },
    { source: 'retrainingTrigger', target: 'modelTrainer', dataType: 'Retraining Signal' },

    // Cross-phase connections
    { source: 'dataQualityMonitor', target: 'alertProcessor', dataType: 'Data Quality Alerts' },
    { source: 'modelPredictor', target: 'predictionsMonitor', dataType: 'Predictions' },
    { source: 'retrainingTrigger', target: 'dataCollector', dataType: 'Data Refresh Signal' }
  ];

  // Refs
  const containerRef = useRef<HTMLDivElement>(null);
  const svgRef = useRef<SVGSVGElement>(null);

  // Handle escape key for closing
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        if (selectedComponent) {
          setSelectedComponent(null);
        } else {
          onClose();
        }
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [onClose, selectedComponent]);

  // Handle mouse movement for subtle parallax effect
  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      if (!containerRef.current) return;
      const rect = containerRef.current.getBoundingClientRect();
      const x = ((e.clientX - rect.left) / rect.width) * 100;
      const y = ((e.clientY - rect.top) / rect.height) * 100;
      setMousePosition({ x, y });
    };
    const container = containerRef.current;
    if (container) {
      container.addEventListener('mousemove', handleMouseMove);
    }
    return () => {
      if (container) {
        container.removeEventListener('mousemove', handleMouseMove);
      }
    };
  }, []);

  // Toggle fullscreen
  const toggleFullscreen = () => {
    if (!document.fullscreenElement) {
      containerRef.current?.requestFullscreen().catch(err => {
        console.error(`Error attempting to enable fullscreen: ${err.message}`);
      });
      setIsFullscreen(true);
    } else {
      document.exitFullscreen();
      setIsFullscreen(false);
    }
  };

  // Handle zoom
  const handleZoomIn = () => {
    setZoomLevel(prev => Math.min(prev + 0.2, 2.5));
  };

  const handleZoomOut = () => {
    setZoomLevel(prev => Math.max(prev - 0.2, 0.5));
  };

  // Handle component selection
  const handleComponentClick = (componentId: string) => {
    setSelectedComponent(prev => prev === componentId ? null : componentId);
  };

  // Handle animation of data flow
  const runDataFlowAnimation = () => {
    setIsAnimating(true);
    setTimeout(() => setIsAnimating(false), 5000); // Animation duration
  };

  // Reset view
  const resetView = () => {
    setZoomLevel(1);
    setSelectedComponent(null);
    setViewMode('conceptual');
  };

  // Render placeholder for now - we'll implement the actual visualization in the next steps
  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-80 backdrop-blur-sm"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          onClick={onClose}
        >
          <motion.div
            ref={containerRef}
            className="relative w-[90vw] h-[85vh] bg-[#121826] rounded-xl overflow-hidden border border-[#7C4DFF]/40 shadow-[0_0_30px_rgba(124,77,255,0.2)]"
            initial={{ scale: 0.9, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            exit={{ scale: 0.9, opacity: 0 }}
            transition={{ type: "spring", damping: 25, stiffness: 300 }}
            onClick={(e) => e.stopPropagation()}
          >
            {/* Header with controls */}
            <div className="absolute top-0 left-0 right-0 px-6 py-4 flex items-center justify-between z-20 bg-gradient-to-b from-[#121826] via-[#121826]/90 to-transparent backdrop-blur-sm">
              <div>
                <h2 className="text-2xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-cyan-400 to-purple-500">
                  {safeTranslations.title}
                </h2>
                <p className="text-indigo-300 text-sm mt-1">
                  {safeTranslations.subtitle}
                </p>
              </div>

              <div className="flex items-center space-x-3">
                {/* View mode toggle */}
                <div className="bg-[#0A1020]/70 border border-[#7C4DFF]/40 rounded-lg overflow-hidden flex shadow-[0_0_10px_rgba(124,77,255,0.15)]">
                  <button
                    className={`px-3 py-1.5 text-sm font-medium transition-all duration-300 ${viewMode === 'conceptual' ? 'bg-[#7C4DFF]/30 text-white' : 'text-[#4FC3F7]/80 hover:text-[#4FC3F7]'}`}
                    onClick={() => setViewMode('conceptual')}
                  >
                    {safeTranslations.viewModes.conceptual}
                  </button>
                  <button
                    className={`px-3 py-1.5 text-sm font-medium transition-all duration-300 ${viewMode === 'implementation' ? 'bg-[#7C4DFF]/30 text-white' : 'text-[#4FC3F7]/80 hover:text-[#4FC3F7]'}`}
                    onClick={() => setViewMode('implementation')}
                  >
                    {safeTranslations.viewModes.implementation}
                  </button>
                </div>

                {/* Zoom controls */}
                <button
                  className="p-2 rounded-full bg-[#0A1020]/70 text-[#4FC3F7] hover:text-white border border-[#4FC3F7]/40 shadow-[0_0_10px_rgba(79,195,247,0.15)] transition-all duration-300 hover:shadow-[0_0_15px_rgba(79,195,247,0.3)] hover:border-[#4FC3F7]/60"
                  onClick={handleZoomIn}
                  aria-label={safeTranslations.buttons.zoomIn}
                >
                  <ZoomIn size={18} />
                </button>
                <button
                  className="p-2 rounded-full bg-[#0A1020]/70 text-[#4FC3F7] hover:text-white border border-[#4FC3F7]/40 shadow-[0_0_10px_rgba(79,195,247,0.15)] transition-all duration-300 hover:shadow-[0_0_15px_rgba(79,195,247,0.3)] hover:border-[#4FC3F7]/60"
                  onClick={handleZoomOut}
                  aria-label={safeTranslations.buttons.zoomOut}
                >
                  <ZoomOut size={18} />
                </button>

                {/* Fullscreen toggle */}
                <button
                  className="p-2 rounded-full bg-[#0A1020]/70 text-[#4FC3F7] hover:text-white border border-[#4FC3F7]/40 shadow-[0_0_10px_rgba(79,195,247,0.15)] transition-all duration-300 hover:shadow-[0_0_15px_rgba(79,195,247,0.3)] hover:border-[#4FC3F7]/60"
                  onClick={toggleFullscreen}
                  aria-label={safeTranslations.buttons.fullscreen}
                >
                  {isFullscreen ? <Minimize2 size={18} /> : <Maximize2 size={18} />}
                </button>

                {/* Close button */}
                <button
                  className="p-2 rounded-full bg-[#0A1020]/70 text-red-400 hover:text-red-300 border border-red-500/40 shadow-[0_0_10px_rgba(248,113,113,0.15)] transition-all duration-300 hover:shadow-[0_0_15px_rgba(248,113,113,0.3)] hover:border-red-500/60"
                  onClick={onClose}
                  aria-label={safeTranslations.buttons.close}
                >
                  <X size={18} />
                </button>
              </div>
            </div>





            {/* Interactive SVG Diagram */}
            <div
              className="absolute overflow-hidden"
              style={{
                top: '80px', // Account for header
                left: '0',
                right: '0',
                bottom: '80px', // Account for footer
                transform: `scale(${zoomLevel})`,
                transformOrigin: 'center',
                transition: 'transform 0.3s ease-out'
              }}
            >
              {/* SVG for connections */}
              <svg
                ref={svgRef}
                className="absolute inset-0 w-full h-full"
                preserveAspectRatio="xMidYMid meet"
                viewBox="0 0 100 100" // Using percentages as coordinate system
              >
                <defs>
                  {/* Enhanced gradient definitions for connection lines */}
                  <linearGradient id="dataIngestionGradient" x1="0%" y1="0%" x2="100%" y2="0%">
                    <stop offset="0%" stopColor="#4FC3F7" stopOpacity="0.9" />
                    <stop offset="50%" stopColor="#4FC3F7" stopOpacity="0.7" />
                    <stop offset="100%" stopColor="#4FC3F7" stopOpacity="0.5" />
                  </linearGradient>
                  <linearGradient id="modelTrainingGradient" x1="0%" y1="0%" x2="100%" y2="0%">
                    <stop offset="0%" stopColor="#7C4DFF" stopOpacity="0.9" />
                    <stop offset="50%" stopColor="#7C4DFF" stopOpacity="0.7" />
                    <stop offset="100%" stopColor="#7C4DFF" stopOpacity="0.5" />
                  </linearGradient>
                  <linearGradient id="deploymentGradient" x1="0%" y1="0%" x2="100%" y2="0%">
                    <stop offset="0%" stopColor="#00E676" stopOpacity="0.9" />
                    <stop offset="50%" stopColor="#00E676" stopOpacity="0.7" />
                    <stop offset="100%" stopColor="#00E676" stopOpacity="0.5" />
                  </linearGradient>
                  <linearGradient id="monitoringGradient" x1="0%" y1="0%" x2="100%" y2="0%">
                    <stop offset="0%" stopColor="#FF4081" stopOpacity="0.9" />
                    <stop offset="50%" stopColor="#FF4081" stopOpacity="0.7" />
                    <stop offset="100%" stopColor="#FF4081" stopOpacity="0.5" />
                  </linearGradient>
                  <linearGradient id="crossPhaseGradient" x1="0%" y1="0%" x2="100%" y2="0%">
                    <stop offset="0%" stopColor="#4FC3F7" stopOpacity="0.9" />
                    <stop offset="50%" stopColor="#7C4DFF" stopOpacity="0.8" />
                    <stop offset="100%" stopColor="#00E676" stopOpacity="0.9" />
                  </linearGradient>

                  {/* Animated gradients */}
                  <linearGradient id="animatedDataGradient" x1="0%" y1="0%" x2="100%" y2="0%">
                    <stop offset="0%" stopColor="#4FC3F7" stopOpacity="0.9">
                      <animate attributeName="offset" values="0;0.3;0" dur="3s" repeatCount="indefinite" />
                    </stop>
                    <stop offset="50%" stopColor="white" stopOpacity="0.9">
                      <animate attributeName="offset" values="0.3;0.6;0.3" dur="3s" repeatCount="indefinite" />
                    </stop>
                    <stop offset="100%" stopColor="#4FC3F7" stopOpacity="0.9">
                      <animate attributeName="offset" values="0.6;1;0.6" dur="3s" repeatCount="indefinite" />
                    </stop>
                  </linearGradient>

                  {/* Enhanced flow markers with better visibility */}
                  <marker
                    id="flowArrow"
                    viewBox="0 0 12 12"
                    refX="6"
                    refY="6"
                    markerWidth="6"
                    markerHeight="6"
                    orient="auto-start-reverse"
                  >
                    <path d="M 2 2 L 10 6 L 2 10 z" fill="white" fillOpacity="0.9" stroke="white" strokeWidth="0.5">
                      <animate attributeName="opacity" values="0.6;1;0.6" dur="2s" repeatCount="indefinite" />
                    </path>
                  </marker>

                  {/* Static arrow markers for better contrast */}
                  <marker
                    id="staticArrow"
                    viewBox="0 0 12 12"
                    refX="6"
                    refY="6"
                    markerWidth="5"
                    markerHeight="5"
                    orient="auto-start-reverse"
                  >
                    <path d="M 2 2 L 10 6 L 2 10 z" fill="white" fillOpacity="0.7" stroke="white" strokeWidth="0.3" />
                  </marker>

                  {/* Enhanced glow filters for nodes */}
                  <filter id="glow" x="-50%" y="-50%" width="200%" height="200%">
                    <feGaussianBlur stdDeviation="2.5" result="blur" />
                    <feComposite in="SourceGraphic" in2="blur" operator="over" />
                  </filter>

                  <filter id="strongGlow" x="-50%" y="-50%" width="200%" height="200%">
                    <feGaussianBlur stdDeviation="4" result="blur" />
                    <feComposite in="SourceGraphic" in2="blur" operator="over" />
                  </filter>

                  <filter id="pulsingGlow" x="-50%" y="-50%" width="200%" height="200%">
                    <feGaussianBlur stdDeviation="3" result="blur">
                      <animate attributeName="stdDeviation" values="2;4;2" dur="3s" repeatCount="indefinite" />
                    </feGaussianBlur>
                    <feComposite in="SourceGraphic" in2="blur" operator="over" />
                  </filter>

                  {/* Pattern for background */}
                  <pattern id="gridPattern" width="40" height="40" patternUnits="userSpaceOnUse">
                    <path d="M 40 0 L 0 0 0 40" fill="none" stroke="#4FC3F7" strokeWidth="0.1" opacity="0.3" />
                  </pattern>
                </defs>

                {/* Background grid pattern */}
                <rect x="0" y="0" width="100" height="100" fill="url(#gridPattern)" />

                {/* Full-width phase containers that visually encompass all nodes positioned at 15%, 35%, 55%, 75% */}
                <rect
                  x="-45" y="2" width="175" height="20" rx="4"
                  fill="url(#dataIngestionGradient)" fillOpacity="0.1"
                  stroke="#4FC3F7" strokeOpacity="0.5" strokeWidth="0.3"
                  filter="url(#glow)"
                />
                <rect
                  x="-45" y="26" width="175" height="20" rx="4"
                  fill="url(#modelTrainingGradient)" fillOpacity="0.1"
                  stroke="#7C4DFF" strokeOpacity="0.5" strokeWidth="0.3"
                  filter="url(#glow)"
                />
                <rect
                  x="-45" y="50" width="175" height="20" rx="4"
                  fill="url(#deploymentGradient)" fillOpacity="0.1"
                  stroke="#00E676" strokeOpacity="0.5" strokeWidth="0.3"
                  filter="url(#glow)"
                />
                <rect
                  x="-45" y="74" width="175" height="20" rx="4"
                  fill="url(#monitoringGradient)" fillOpacity="0.1"
                  stroke="#FF4081" strokeOpacity="0.5" strokeWidth="0.3"
                  filter="url(#glow)"
                />

                {/* Phase labels positioned inside the extended containers */}
                <text x="-40" y="8" fontSize="2.2" fill="#3B82F6" fontWeight="bold">{safeTranslations.phases.dataIngestion}</text>
                <text x="-40" y="32" fontSize="2.2" fill="#8B5CF6" fontWeight="bold">{safeTranslations.phases.modelTraining}</text>
                <text x="-40" y="56" fontSize="2.2" fill="#10B981" fontWeight="bold">{safeTranslations.phases.deployment}</text>
                <text x="-40" y="80" fontSize="2.2" fill="#EC4899" fontWeight="bold">{safeTranslations.phases.monitoring}</text>

                {/* Render connections */}
                {mlConnections.map((conn, index) => {
                  const sourceComponent = mlComponents.find(c => c.id === conn.source);
                  const targetComponent = mlComponents.find(c => c.id === conn.target);

                  if (!sourceComponent || !targetComponent) return null;

                  // Determine connection color based on source component phase
                  let gradientId;
                  if (sourceComponent.phase === targetComponent.phase) {
                    // Same phase connections
                    switch (sourceComponent.phase) {
                      case 'dataIngestion': gradientId = 'dataIngestionGradient'; break;
                      case 'modelTraining': gradientId = 'modelTrainingGradient'; break;
                      case 'deployment': gradientId = 'deploymentGradient'; break;
                      case 'monitoring': gradientId = 'monitoringGradient'; break;
                      default: gradientId = 'crossPhaseGradient';
                    }
                  } else {
                    // Cross-phase connections
                    gradientId = 'crossPhaseGradient';
                  }

                  // Calculate clean connection paths with proper anchor points
                  const startX = sourceComponent.x;
                  const startY = sourceComponent.y;
                  const endX = targetComponent.x;
                  const endY = targetComponent.y;

                  // Create simple, clean paths that avoid text overlaps
                  let path: string;

                  if (sourceComponent.phase === targetComponent.phase) {
                    // Same phase - simple curved connection
                    const midX = (startX + endX) / 2;
                    const curveOffset = 1; // Minimal curve for smooth flow
                    path = `M ${startX} ${startY} Q ${midX} ${startY - curveOffset} ${endX} ${endY}`;
                  } else {
                    // Cross-phase - simple diagonal with curve to avoid sharp angles
                    const midX = (startX + endX) / 2;
                    const midY = (startY + endY) / 2;
                    path = `M ${startX} ${startY} Q ${midX} ${midY} ${endX} ${endY}`;
                  }

                  // Determine if this connection should be animated
                  const isAnimated = isAnimating || conn.animated;

                  return (
                    <g key={`conn-${index}`}>
                      {/* Clean connection line with improved visibility */}
                      <path
                        d={path}
                        stroke={isAnimated ? `url(#animatedDataGradient)` : `url(#${gradientId})`}
                        strokeWidth={selectedComponent === sourceComponent.id || selectedComponent === targetComponent.id ? "0.6" : "0.4"}
                        strokeOpacity={selectedComponent ?
                          (selectedComponent === sourceComponent.id || selectedComponent === targetComponent.id ? 1 : 0.3)
                          : 0.8}
                        fill="none"
                        strokeDasharray={sourceComponent.phase !== targetComponent.phase ? "1,1" : "none"}
                        filter={selectedComponent === sourceComponent.id || selectedComponent === targetComponent.id ? "url(#glow)" : "none"}
                        markerEnd={isAnimated ? "url(#flowArrow)" : "url(#staticArrow)"}
                      />

                      {/* Connection highlight glow for selected components */}
                      {(selectedComponent === sourceComponent.id || selectedComponent === targetComponent.id) && (
                        <path
                          d={path}
                          stroke={`url(#${gradientId})`}
                          strokeWidth="0.2"
                          strokeOpacity="0.5"
                          fill="none"
                          filter="url(#pulsingGlow)"
                        />
                      )}

                      {/* Enhanced data flow animation */}
                      {isAnimated && (
                        <>
                          <circle r="0.6" fill="white" fillOpacity="0.9" filter="url(#glow)">
                            <animateMotion
                              dur="3s"
                              repeatCount="indefinite"
                              path={path}
                            />
                          </circle>
                          <text
                            fontSize="1.6"
                            fill="white"
                            fillOpacity="0.9"
                            textAnchor="middle"
                            dominantBaseline="middle"
                            filter="url(#glow)"
                          >
                            <textPath href={`#data-path-${index}`} startOffset="50%">
                              {conn.dataType}
                            </textPath>
                          </text>
                        </>
                      )}

                      {/* Path for text labels with better visibility */}
                      <path
                        id={`data-path-${index}`}
                        d={path}
                        stroke="none"
                        fill="none"
                      />

                      {/* Always show data type label with better visibility */}
                      {!isAnimated && conn.dataType && (
                        <text
                          fontSize="1.2"
                          fill="white"
                          fillOpacity={selectedComponent === sourceComponent.id || selectedComponent === targetComponent.id ? "0.9" : "0.6"}
                          textAnchor="middle"
                          dominantBaseline="middle"
                        >
                          <textPath href={`#data-path-${index}`} startOffset="50%">
                            {conn.dataType}
                          </textPath>
                        </text>
                      )}
                    </g>
                  );
                })}
              </svg>

              {/* Component nodes */}
              {mlComponents.map((component) => {
                const isSelected = selectedComponent === component.id;

                return (
                  <Tooltip.Provider key={component.id}>
                    <Tooltip.Root delayDuration={200}>
                      <Tooltip.Trigger asChild>
                        <motion.div
                          className="absolute cursor-pointer"
                          style={{
                            left: `${component.x}%`,
                            top: `${component.y}%`,
                            width: '120px',
                            height: '120px',
                            marginLeft: '-60px', // Half of width to center
                            marginTop: '-60px',  // Half of height to center
                            zIndex: isSelected ? 30 : 20
                          }}
                          initial={{ scale: 0, opacity: 0 }}
                          animate={{
                            scale: isSelected ? 1.2 : 1,
                            opacity: 1,
                            x: isSelected ? 0 : (mousePosition.x - 50) * 0.01, // Subtle parallax effect
                            y: isSelected ? 0 : (mousePosition.y - 50) * 0.01
                          }}
                          transition={{
                            type: "spring",
                            stiffness: 300,
                            damping: 20,
                            delay: 0.1 * mlComponents.indexOf(component)
                          }}
                          onClick={(e) => {
                            e.stopPropagation();
                            handleComponentClick(component.id);
                          }}
                          whileHover={{ scale: 1.1 }}
                        >
                          <div
                            className="w-14 h-14 rounded-full flex items-center justify-center shadow-xl transition-all duration-300 absolute top-1/2 left-1/2"
                            style={{
                              border: `2px solid ${isSelected ? 'white' : 'rgba(255, 255, 255, 0.4)'}`,
                              borderWidth: isSelected ? '3px' : '2px',
                              background: `radial-gradient(circle at center, ${component.color}50, ${component.color}30)`,
                              boxShadow: isSelected
                                ? `0 0 30px ${component.color}, 0 0 20px ${component.color}, inset 0 0 10px rgba(255, 255, 255, 0.2)`
                                : `0 0 20px ${component.color}90, inset 0 0 8px rgba(255, 255, 255, 0.1)`,
                              transform: `translate(-50%, -50%) scale(${isSelected ? 1.1 : 1})`,
                              filter: isSelected ? 'url(#strongGlow)' : 'url(#glow)'
                            }}
                          >
                            <span
                              className="text-3xl font-bold"
                              style={{
                                filter: 'drop-shadow(0 0 8px white) drop-shadow(0 0 4px rgba(255, 255, 255, 0.8))',
                                textShadow: '0 0 10px white, 0 0 5px rgba(255, 255, 255, 0.8)'
                              }}
                            >
                              {component.icon}
                            </span>
                          </div>
                          <div
                            className="absolute left-1/2 whitespace-nowrap transition-all duration-300"
                            style={{
                              top: 'calc(50% + 28px + 8px)', // 50% (center) + 28px (half node height) + 8px (gap)
                              opacity: isSelected ? 1 : 0.95,
                              transform: `translateX(-50%) translateY(${isSelected ? '2px' : '0'}) scale(${isSelected ? 1.1 : 1})`,
                              transformOrigin: 'center top'
                            }}
                          >
                            <span
                              className="text-white text-sm font-semibold px-3 py-1.5 rounded-md border inline-block"
                              style={{
                                background: `linear-gradient(to right, ${component.color}95, ${component.color}70)`,
                                boxShadow: `0 0 15px ${component.color}60, inset 0 0 5px rgba(255, 255, 255, 0.2)`,
                                backdropFilter: 'blur(6px)',
                                borderColor: 'rgba(255, 255, 255, 0.3)',
                                textShadow: '0 0 8px rgba(255, 255, 255, 0.8), 0 1px 2px rgba(0, 0, 0, 0.5)',
                                textAlign: 'center'
                              }}
                            >
                              {component.name}
                            </span>
                          </div>
                        </motion.div>
                      </Tooltip.Trigger>
                      <Tooltip.Portal>
                        <Tooltip.Content
                          className="z-50 max-w-xs bg-[#0A1020]/95 border-2 rounded-md p-4 shadow-2xl backdrop-blur-md"
                          style={{
                            borderColor: component.color,
                            boxShadow: `0 0 20px ${component.color}50`
                          }}
                          side="top"
                          sideOffset={5}
                          align="center"
                        >
                          <div className="text-sm text-gray-300 font-open-sans">
                            <p
                              className="font-bold text-white mb-2 text-base"
                              style={{
                                color: component.color,
                                textShadow: `0 0 8px ${component.color}80`
                              }}
                            >
                              {component.name}
                            </p>
                            <p className="leading-relaxed">{component.description}</p>
                            {viewMode === 'implementation' && component.technologies && (
                              <div className="mt-3 pt-3 border-t border-gray-700">
                                <p className="text-xs text-[#4FC3F7] font-semibold mb-1">Technologies:</p>
                                <div className="flex flex-wrap gap-1 mt-1">
                                  {component.technologies.map((tech, idx) => (
                                    <span
                                      key={idx}
                                      className="text-xs px-2 py-0.5 rounded-full bg-[#7C4DFF]/20 border border-[#7C4DFF]/40"
                                    >
                                      {tech}
                                    </span>
                                  ))}
                                </div>
                              </div>
                            )}
                          </div>
                          <Tooltip.Arrow
                            className="fill-[#0A1020]/95"
                            style={{
                              filter: `drop-shadow(0 0 5px ${component.color}50)`
                            }}
                          />
                        </Tooltip.Content>
                      </Tooltip.Portal>
                    </Tooltip.Root>
                  </Tooltip.Provider>
                );
              })}
            </div>

            {/* Footer with controls */}
            <div className="absolute bottom-0 left-0 right-0 px-6 py-4 flex items-center justify-between z-20 bg-gradient-to-t from-[#121826] via-[#121826]/90 to-transparent backdrop-blur-sm">
              <button
                className="px-5 py-2.5 rounded-lg bg-gradient-to-r from-[#7C4DFF]/90 to-[#4FC3F7]/90 text-white text-sm font-medium shadow-lg shadow-[#7C4DFF]/30 hover:shadow-[#7C4DFF]/50 transition-all duration-300 hover:translate-y-[-2px] border border-white/10"
                onClick={runDataFlowAnimation}
                style={{
                  fontFamily: 'Montserrat, sans-serif',
                  boxShadow: '0 0 15px rgba(124, 77, 255, 0.3), inset 0 0 10px rgba(79, 195, 247, 0.2)'
                }}
              >
                {safeTranslations.buttons.runData}
              </button>

              <button
                className="px-4 py-2 rounded-lg bg-deep-space border border-indigo-500/30 text-indigo-300 text-sm"
                onClick={resetView}
              >
                {safeTranslations.buttons.reset}
              </button>
            </div>
          </motion.div>

          {/* Component Detail Modal */}
          <AnimatePresence>
            {selectedComponent && (
              <MLComponentDetail
                component={mlComponents.find(c => c.id === selectedComponent) || null}
                viewMode={viewMode}
                onClose={() => setSelectedComponent(null)}
                translations={{
                  keyFeatures: translations.components?.keyFeatures || 'Key Features',
                  benefits: translations.components?.benefits || 'Benefits',
                  implementationTech: translations.components?.implementationTech || 'Implementation Technologies',
                  technicalConsiderations: translations.components?.technicalConsiderations || 'Technical Considerations',
                  technicalConsiderationsList: [translations.components?.technicalConsiderations || 'Technical considerations for this component'],
                  componentWorkflow: translations.components?.componentWorkflow || 'Component Workflow',
                  implementationArchitecture: translations.components?.implementationArchitecture || 'Implementation Architecture',
                  comingSoon: translations.components?.comingSoon || 'Coming Soon',
                  learnMore: translations.components?.learnMore || 'Learn More'
                }}
              />
            )}
          </AnimatePresence>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default InteractiveMLArchitecture;
