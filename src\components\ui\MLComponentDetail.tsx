'use client';

import React, { useEffect } from 'react';
import { X } from 'lucide-react';
import { motion } from 'framer-motion';

interface MLComponentDetailTranslations {
  keyFeatures: string;
  benefits: string;
  implementationTech: string;
  technicalConsiderations: string;
  componentWorkflow: string;
  implementationArchitecture: string;
  comingSoon: string;
  learnMore: string;
  technicalConsiderationsList: string[];
}

interface MLComponentDetailProps {
  component: {
    id: string;
    name: string;
    description: string;
    icon?: string;
    color?: string;
    features?: string[];
    benefits?: string[];
    technologies?: string[];
  } | null;
  viewMode: 'conceptual' | 'implementation';
  onClose: () => void;
  translations: MLComponentDetailTranslations;
}

const MLComponentDetail: React.FC<MLComponentDetailProps> = ({
  component,
  viewMode,
  onClose,
  translations
}) => {
  // Add keyboard event listener for Escape key
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        onClose();
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => {
      window.removeEventListener('keydown', handleKeyDown);
    };
  }, [onClose]);

  // Handle backdrop click
  const handleBackdropClick = (e: React.MouseEvent) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  // Animation variants
  const modalVariants = {
    hidden: { opacity: 0, y: 20, scale: 0.95 },
    visible: {
      opacity: 1,
      y: 0,
      scale: 1,
      transition: {
        type: "spring",
        damping: 25,
        stiffness: 500
      }
    },
    exit: {
      opacity: 0,
      y: 20,
      scale: 0.95,
      transition: { duration: 0.2 }
    }
  };

  if (!component) return null;

  return (
    <div
      className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-[60]"
      onClick={handleBackdropClick}
    >
      <motion.div
        className="mt-6 p-6 rounded-lg border border-white/20 bg-gradient-to-br from-gray-900 to-black/60 backdrop-blur-sm max-w-4xl w-full mx-4 relative"
        style={{
          boxShadow: '0 0 20px rgba(99, 102, 241, 0.5), 0 0 40px rgba(99, 102, 241, 0.2), inset 0 0 15px rgba(255, 255, 255, 0.1)',
          border: '1px solid rgba(255, 255, 255, 0.2)'
        }}
        onClick={(e) => e.stopPropagation()}
        variants={modalVariants}
        initial="hidden"
        animate="visible"
        exit="exit"
      >
        <button
          onClick={onClose}
          className="absolute top-4 right-4 text-gray-400 hover:text-white transition-colors"
          aria-label="Close"
        >
          <X size={24} />
        </button>

        <div className="flex items-start gap-4 mb-6">
          <div
            className="text-3xl bg-gradient-to-br from-cyan-400 to-purple-500 rounded-full h-14 w-14 flex items-center justify-center"
            style={{
              boxShadow: '0 0 10px rgba(6, 182, 212, 0.7), 0 0 20px rgba(168, 85, 247, 0.5), inset 0 0 5px rgba(255, 255, 255, 0.5)',
              border: '1px solid rgba(255, 255, 255, 0.3)',
              textShadow: '0 0 5px rgba(255, 255, 255, 0.8)'
            }}
          >
            <span style={{
              filter: 'brightness(1.2) contrast(1.2)',
              color: '#ffffff'
            }}>{component.icon}</span>
          </div>

          <div>
            <h3 className="text-xl md:text-2xl font-semibold text-white mb-3">{component.name}</h3>
            <p className="mb-6 text-gray-300">{component.description}</p>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {viewMode === 'conceptual' ? (
            <>
              <div className="p-4 bg-black/50 rounded-lg border border-white/10"
                style={{
                  boxShadow: '0 0 10px rgba(6, 182, 212, 0.3), inset 0 0 5px rgba(6, 182, 212, 0.1)',
                  background: 'linear-gradient(135deg, rgba(6, 182, 212, 0.1), rgba(0, 0, 0, 0.7))'
                }}>
                <h4 className="font-semibold text-cyan-400 mb-2" style={{ textShadow: '0 0 5px rgba(6, 182, 212, 0.8)' }}>
                  {translations.keyFeatures}
                </h4>
                <ul className="space-y-2 text-gray-300">
                  {component.features?.map((feature, index) => (
                    <li key={`feature-${index}`} className="flex items-start">
                      <span className="text-cyan-400 mr-2" style={{ textShadow: '0 0 3px rgba(6, 182, 212, 0.8)' }}>→</span>
                      <span>{feature}</span>
                    </li>
                  ))}
                </ul>
              </div>
              <div className="p-4 bg-black/50 rounded-lg border border-white/10"
                style={{
                  boxShadow: '0 0 10px rgba(236, 72, 153, 0.3), inset 0 0 5px rgba(236, 72, 153, 0.1)',
                  background: 'linear-gradient(135deg, rgba(236, 72, 153, 0.1), rgba(0, 0, 0, 0.7))'
                }}>
                <h4 className="font-semibold text-pink-400 mb-2" style={{ textShadow: '0 0 5px rgba(236, 72, 153, 0.8)' }}>
                  {translations.benefits}
                </h4>
                <ul className="space-y-2 text-gray-300">
                  {component.benefits?.map((benefit, index) => (
                    <li key={`benefit-${index}`} className="flex items-start">
                      <span className="text-pink-400 mr-2" style={{ textShadow: '0 0 3px rgba(236, 72, 153, 0.8)' }}>→</span>
                      <span>{benefit}</span>
                    </li>
                  ))}
                </ul>
              </div>
            </>
          ) : (
            <>
              <div className="p-4 bg-black/50 rounded-lg border border-white/10"
                style={{
                  boxShadow: '0 0 10px rgba(6, 182, 212, 0.3), inset 0 0 5px rgba(6, 182, 212, 0.1)',
                  background: 'linear-gradient(135deg, rgba(6, 182, 212, 0.1), rgba(0, 0, 0, 0.7))'
                }}>
                <h4 className="font-semibold text-cyan-400 mb-2" style={{ textShadow: '0 0 5px rgba(6, 182, 212, 0.8)' }}>
                  {translations.implementationTech}
                </h4>
                <ul className="space-y-2 text-gray-300">
                  {component.technologies?.map((tech, index) => (
                    <li key={`tech-${index}`} className="flex items-start">
                      <span className="text-cyan-400 mr-2" style={{ textShadow: '0 0 3px rgba(6, 182, 212, 0.8)' }}>→</span>
                      <span>{tech}</span>
                    </li>
                  ))}
                </ul>
              </div>
              <div className="p-4 bg-black/50 rounded-lg border border-white/10"
                style={{
                  boxShadow: '0 0 10px rgba(236, 72, 153, 0.3), inset 0 0 5px rgba(236, 72, 153, 0.1)',
                  background: 'linear-gradient(135deg, rgba(236, 72, 153, 0.1), rgba(0, 0, 0, 0.7))'
                }}>
                <h4 className="font-semibold text-pink-400 mb-2" style={{ textShadow: '0 0 5px rgba(236, 72, 153, 0.8)' }}>
                  {translations.technicalConsiderations}
                </h4>
                <ul className="space-y-2 text-gray-300">
                  {translations.technicalConsiderationsList.map((item, index) => (
                    <li key={`tc-${index}`} className="flex items-start">
                      <span className="text-pink-400 mr-2" style={{ textShadow: '0 0 3px rgba(236, 72, 153, 0.8)' }}>→</span>
                      <span>{item}</span>
                    </li>
                  ))}
                </ul>
              </div>
            </>
          )}
        </div>
      </motion.div>
    </div>
  );
};

export default MLComponentDetail;
